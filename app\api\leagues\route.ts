import { NextRequest, NextResponse } from 'next/server';
import { LeagueOperations } from '@/lib/db/operations';

export async function GET() {
  try {
    const leagues = await LeagueOperations.findAll();
    return NextResponse.json({
      success: true,
      data: leagues
    });
  } catch (error) {
    console.error('Error fetching leagues:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch leagues',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, slug, data = [] } = body;

    if (!name || !slug) {
      return NextResponse.json(
        {
          success: false,
          message: 'Name and slug are required'
        },
        { status: 400 }
      );
    }

    // Check if league already exists
    const existingLeague = await LeagueOperations.findBySlug(slug);
    if (existingLeague) {
      return NextResponse.json(
        {
          success: false,
          message: 'League with this slug already exists'
        },
        { status: 409 }
      );
    }

    const league = await LeagueOperations.create({
      name,
      slug,
      data
    });

    return NextResponse.json({
      success: true,
      data: league
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating league:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create league',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
