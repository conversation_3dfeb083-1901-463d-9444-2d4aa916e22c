import Link from 'next/link';

export default function Home() {
  const data = [
    { id: 1, name: "Team Alpha", MP: 5, W: 4, L: 1, SF: 12, SA: 6, SD: 6, Pts: 12 },
    { id: 2, name: "Team Bravo", MP: 5, W: 3, L: 2, SF: 10, SA: 7, SD: 3, Pts: 9 },
    { id: 3, name: "Team Charlie", MP: 5, W: 2, L: 3, SF: 8,  SA: 9, SD: -1, Pts: 6 },
    { id: 4, name: "Team Delta", MP: 5, W: 1, L: 4, SF: 5,  SA: 13, SD: -8, Pts: 3 },
  ];

  return (
    <main className="min-h-screen bg-slate-100 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Navigation */}
        <div className="bg-white rounded-2xl shadow-lg p-4">
          <div className="flex flex-wrap gap-4">
            <Link
              href="/leagues"
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition"
            >
              🏆 All Leagues
            </Link>
            <Link
              href="/leagues/tes-liga"
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
            >
              📊 Tes Liga
            </Link>
            <Link
              href="/players"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
              👥 Players
            </Link>
            <Link
              href="/api/test-db"
              target="_blank"
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition"
            >
              🔗 Test Database
            </Link>
          </div>
        </div>

        {/* Leaderboard */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="bg-blue-600 p-4">
            <h1 className="text-2xl font-bold text-white">🏆 Domino League — Leaderboard</h1>
          </div>
        <div className="p-4">
          <table className="w-full border-collapse text-gray-700">
            <thead>
              <tr className="bg-gray-200">
                <th className="p-2 text-left">#</th>
                <th className="p-2 text-left">Team</th>
                <th className="p-2">MP</th>
                <th className="p-2">W</th>
                <th className="p-2">L</th>
                <th className="p-2">SF</th>
                <th className="p-2">SA</th>
                <th className="p-2">SD</th>
                <th className="p-2">Pts</th>
              </tr>
            </thead>
            <tbody>
              {data.map((row, idx) => (
                <tr 
                  key={row.id} 
                  className="border-b hover:bg-blue-50 transition"
                >
                  <td className="p-2 font-medium">{idx + 1}</td>
                  <td className="p-2">{row.name}</td>
                  <td className="p-2 text-center">{row.MP}</td>
                  <td className="p-2 text-center">{row.W}</td>
                  <td className="p-2 text-center">{row.L}</td>
                  <td className="p-2 text-center">{row.SF}</td>
                  <td className="p-2 text-center">{row.SA}</td>
                  <td className={`p-2 text-center ${row.SD >= 0 ? "text-green-600" : "text-red-600"}`}>
                    {row.SD}
                  </td>
                  <td className="p-2 text-center font-bold">{row.Pts}</td>
                </tr>
              ))}
            </tbody>
          </table>
          </div>
        </div>
      </div>
    </main>
  );
}