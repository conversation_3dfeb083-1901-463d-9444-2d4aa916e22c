# MongoDB Setup untuk Domino League

## Konfigurasi Database

### 1. Environment Variables
Buat file `.env.local` di root project dengan konfigurasi berikut:

```env
# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DB_NAME=domino
```

### 2. MongoDB Local Setup
Untuk menggunakan MongoDB lokal:

1. Install MongoDB Community Server dari [mongodb.com](https://www.mongodb.com/try/download/community)
2. Jalankan MongoDB service
3. Database akan otomatis dibuat saat aplikasi pertama kali dijalankan

### 3. MongoDB Atlas (Cloud) Setup
Untuk menggunakan MongoDB Atlas:

1. Buat akun di [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Buat cluster baru
3. Dapatkan connection string
4. Update `.env.local`:

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DB_NAME=domino
```

## Struktur Database

### Collections:
- **leagues**: Data liga dengan pemain dan statistik
- **games**: Record permainan
- **players**: Data individual pemain

### Models:
- `League`: Liga dengan array data pemain (MP, W, L, SF, SA, SD, Pts)
- `LeaguePlayer`: Data pemain dalam liga (statistik lengkap)
- `Game`: Record permainan (pemain, skor, pemenang, tanggal)
- `Player`: Data individual pemain (nama, email, playerId)

## Testing Koneksi

### 1. Test Database Connection
```bash
curl http://localhost:3000/api/test-db
```

### 2. Test League Operations
```bash
# Get all leagues
curl http://localhost:3000/api/leagues

# Get specific league
curl http://localhost:3000/api/leagues/tes-liga

# Create new league
curl -X POST http://localhost:3000/api/leagues \
  -H "Content-Type: application/json" \
  -d '{"name": "New League", "slug": "new-league", "data": []}'

# Get games for a league
curl http://localhost:3000/api/games?league=tes-liga
```

## File Structure

```
lib/
├── mongodb.ts          # MongoDB connection utility
└── db/
    ├── models.ts       # TypeScript interfaces
    └── operations.ts   # Database operations

app/
├── leagues/
│   ├── page.tsx       # All leagues page
│   └── [slug]/
│       └── page.tsx   # Individual league page
├── players/
│   └── page.tsx       # Players management
└── api/
    ├── test-db/
    │   └── route.ts   # Test database connection
    ├── leagues/
    │   ├── route.ts   # League CRUD operations
    │   └── [slug]/
    │       └── route.ts # Individual league operations
    ├── players/
    │   └── route.ts   # Player CRUD operations
    └── games/
        └── route.ts   # Game CRUD operations
```

## Usage Examples

### Menggunakan Database Operations

```typescript
import { LeagueOperations, GameOperations, PlayerOperations } from '@/lib/db/operations';

// Create league
const league = await LeagueOperations.create({
  name: "My League",
  slug: "my-league",
  data: [
    { id: 1, name: "Player 1", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 }
  ]
});

// Find league by slug
const foundLeague = await LeagueOperations.findBySlug("my-league");

// Create game (automatically updates league stats)
const game = await GameOperations.create({
  leagueSlug: "my-league",
  player1Id: 1,
  player2Id: 2,
  player1Score: 150,
  player2Score: 120,
  winnerId: 1,
  gameDate: new Date()
});

// Create individual player
const player = await PlayerOperations.create({
  name: "John Doe",
  playerId: 1001,
  email: "<EMAIL>"
});
```

## Troubleshooting

### Common Issues:

1. **Connection Error**: Pastikan MongoDB service berjalan
2. **Authentication Error**: Periksa username/password di connection string
3. **Network Error**: Periksa firewall dan network access di MongoDB Atlas

### Debug Mode:
Set environment variable untuk debugging:
```env
DEBUG=mongodb:*
```
