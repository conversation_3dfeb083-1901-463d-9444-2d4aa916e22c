# MongoDB Setup untuk Domino League

## Konfigurasi Database

### 1. Environment Variables
Buat file `.env.local` di root project dengan konfigurasi berikut:

```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=domino-league
```

### 2. MongoDB Local Setup
Untuk menggunakan MongoDB lokal:

1. Install MongoDB Community Server dari [mongodb.com](https://www.mongodb.com/try/download/community)
2. Jalankan MongoDB service
3. Database akan otomatis dibuat saat aplikasi pertama kali dijalankan

### 3. MongoDB Atlas (Cloud) Setup
Untuk menggunakan MongoDB Atlas:

1. Buat akun di [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Buat cluster baru
3. Dapatkan connection string
4. Update `.env.local`:

```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DB_NAME=domino-league
```

## Struktur Database

### Collections:
- **players**: Data pemain domino
- **games**: Record permainan
- **tournaments**: Data turnamen

### Models:
- `Player`: Informasi pemain (nama, email, rating, statistik)
- `Game`: Record permainan (pemain, skor, pemenang, tanggal)
- `Tournament`: Data turnamen (nama, peserta, status)

## Testing Koneksi

### 1. Test Database Connection
```bash
curl http://localhost:3000/api/test-db
```

### 2. Test Player Operations
```bash
# Get all players
curl http://localhost:3000/api/players

# Create new player
curl -X POST http://localhost:3000/api/players \
  -H "Content-Type: application/json" \
  -d '{"name": "John Doe", "email": "<EMAIL>", "rating": 1200}'
```

## File Structure

```
lib/
├── mongodb.ts          # MongoDB connection utility
└── db/
    ├── models.ts       # TypeScript interfaces
    └── operations.ts   # Database operations

app/api/
├── test-db/
│   └── route.ts       # Test database connection
└── players/
    └── route.ts       # Player CRUD operations
```

## Usage Examples

### Menggunakan Database Operations

```typescript
import { PlayerOperations, GameOperations } from '@/lib/db/operations';

// Create player
const player = await PlayerOperations.create({
  name: "Jane Doe",
  email: "<EMAIL>",
  rating: 1000,
  gamesPlayed: 0,
  wins: 0,
  losses: 0
});

// Find player
const foundPlayer = await PlayerOperations.findById(player._id!);

// Create game
const game = await GameOperations.create({
  player1Id: player1._id!,
  player2Id: player2._id!,
  player1Score: 150,
  player2Score: 120,
  winnerId: player1._id!,
  gameDate: new Date()
});
```

## Troubleshooting

### Common Issues:

1. **Connection Error**: Pastikan MongoDB service berjalan
2. **Authentication Error**: Periksa username/password di connection string
3. **Network Error**: Periksa firewall dan network access di MongoDB Atlas

### Debug Mode:
Set environment variable untuk debugging:
```env
DEBUG=mongodb:*
```
