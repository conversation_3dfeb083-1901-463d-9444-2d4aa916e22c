import { getDatabase, closeConnection } from '../lib/mongodb';
import { PlayerOperations, GameOperations, TournamentOperations } from '../lib/db/operations';

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...');

    // Test connection
    const db = await getDatabase();
    await db.admin().ping();
    console.log('✅ Database connection successful');

    // Create sample players
    console.log('👥 Creating sample players...');
    
    const players = await Promise.all([
      PlayerOperations.create({
        name: '<PERSON>',
        email: '<EMAIL>',
        rating: 1200,
        gamesPlayed: 0,
        wins: 0,
        losses: 0
      }),
      PlayerOperations.create({
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        rating: 1150,
        gamesPlayed: 0,
        wins: 0,
        losses: 0
      }),
      PlayerOperations.create({
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        rating: 1300,
        gamesPlayed: 0,
        wins: 0,
        losses: 0
      }),
      PlayerOperations.create({
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        rating: 1100,
        gamesPlayed: 0,
        wins: 0,
        losses: 0
      })
    ]);

    console.log(`✅ Created ${players.length} sample players`);

    // Create sample games
    console.log('🎮 Creating sample games...');
    
    const games = await Promise.all([
      GameOperations.create({
        player1Id: players[0]._id!,
        player2Id: players[1]._id!,
        player1Score: 150,
        player2Score: 120,
        winnerId: players[0]._id!,
        gameDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        duration: 45
      }),
      GameOperations.create({
        player1Id: players[2]._id!,
        player2Id: players[3]._id!,
        player1Score: 200,
        player2Score: 180,
        winnerId: players[2]._id!,
        gameDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        duration: 60
      }),
      GameOperations.create({
        player1Id: players[0]._id!,
        player2Id: players[2]._id!,
        player1Score: 130,
        player2Score: 160,
        winnerId: players[2]._id!,
        gameDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        duration: 50
      })
    ]);

    console.log(`✅ Created ${games.length} sample games`);

    // Create sample tournament
    console.log('🏆 Creating sample tournament...');
    
    const tournament = await TournamentOperations.create({
      name: 'Turnamen Domino Bulanan',
      description: 'Turnamen domino bulanan untuk semua pemain',
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      participants: players.map(p => p._id!),
      status: 'upcoming'
    });

    console.log('✅ Created sample tournament');

    // Display summary
    console.log('\n📊 Database initialization complete!');
    console.log('Summary:');
    console.log(`- Players: ${players.length}`);
    console.log(`- Games: ${games.length}`);
    console.log(`- Tournaments: 1`);
    
    console.log('\n🔗 Test endpoints:');
    console.log('- GET /api/test-db - Test database connection');
    console.log('- GET /api/players - Get all players');
    console.log('- POST /api/players - Create new player');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    await closeConnection();
    console.log('🔌 Database connection closed');
  }
}

// Run initialization if this script is executed directly
if (require.main === module) {
  initializeDatabase();
}

export default initializeDatabase;
