import { getDatabase, closeConnection } from '../lib/mongodb';
import { LeagueOperations, PlayerOperations, GameOperations } from '../lib/db/operations';

async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...');

    // Test connection
    const db = await getDatabase();
    await db.admin().ping();
    console.log('✅ Database connection successful');

    // Create sample league with data from your example
    console.log('🏆 Creating Tes Liga...');

    const leagueData = [
      { id: 5001, name: "<PERSON>", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5002, name: "<PERSON>", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5003, name: "<PERSON>", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5004, name: "<PERSON><PERSON><PERSON>", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5005, name: "<PERSON>", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5006, name: "Brent", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5007, name: "Paul", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5008, name: "Patrick", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5009, name: "Mark", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5010, name: "William", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5011, name: "John", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5012, name: "Martin", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5013, name: "Brian", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5014, name: "Garrett", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5015, name: "Andrew", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5016, name: "Travis", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5017, name: "Nicholas", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5018, name: "Anthony", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5019, name: "Ronnie", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 },
      { id: 5020, name: "Jeremy", MP: 0, W: 0, L: 0, SF: 0, SA: 0, SD: 0, Pts: 0 }
    ];

    const league = await LeagueOperations.create({
      name: "Tes Liga",
      slug: "tes-liga",
      data: leagueData
    });

    console.log('✅ Created Tes Liga with 20 players');

    // Create individual player records
    console.log('👥 Creating individual player records...');

    const playerPromises = leagueData.map(playerData =>
      PlayerOperations.create({
        name: playerData.name,
        playerId: playerData.id,
        email: `${playerData.name.toLowerCase()}@example.com`
      })
    );

    const players = await Promise.all(playerPromises);
    console.log(`✅ Created ${players.length} individual player records`);

    // Create some sample games
    console.log('🎮 Creating sample games...');

    const sampleGames = [
      {
        leagueSlug: "tes-liga",
        player1Id: 5001, // Jack
        player2Id: 5002, // Daniel
        player1Score: 150,
        player2Score: 120,
        winnerId: 5001,
        gameDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        duration: 45
      },
      {
        leagueSlug: "tes-liga",
        player1Id: 5003, // Joshua
        player2Id: 5004, // Kristopher
        player1Score: 200,
        player2Score: 180,
        winnerId: 5003,
        gameDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        duration: 60
      },
      {
        leagueSlug: "tes-liga",
        player1Id: 5001, // Jack
        player2Id: 5003, // Joshua
        player1Score: 130,
        player2Score: 160,
        winnerId: 5003,
        gameDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        duration: 50
      }
    ];

    const games = await Promise.all(
      sampleGames.map(gameData => GameOperations.create(gameData))
    );

    console.log(`✅ Created ${games.length} sample games`);

    // Display summary
    console.log('\n📊 Database initialization complete!');
    console.log('Summary:');
    console.log(`- League: ${league.name} (${league.slug})`);
    console.log(`- League Players: ${league.data.length}`);
    console.log(`- Individual Players: ${players.length}`);
    console.log(`- Games: ${games.length}`);

    console.log('\n🔗 Test endpoints:');
    console.log('- GET /api/test-db - Test database connection');
    console.log('- GET /api/leagues/tes-liga - Get Tes Liga');
    console.log('- GET /api/players - Get all players');
    console.log('- GET /api/games?league=tes-liga - Get league games');
    console.log('- Visit: http://localhost:3000/leagues/tes-liga');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    await closeConnection();
    console.log('🔌 Database connection closed');
  }
}

// Run initialization if this script is executed directly
if (require.main === module) {
  initializeDatabase();
}

export default initializeDatabase;
