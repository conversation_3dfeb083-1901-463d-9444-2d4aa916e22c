// Database Models dan Types untuk Domino League

export interface Player {
  _id?: string;
  name: string;
  email: string;
  rating: number;
  gamesPlayed: number;
  wins: number;
  losses: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Game {
  _id?: string;
  player1Id: string;
  player2Id: string;
  player1Score: number;
  player2Score: number;
  winnerId: string;
  gameDate: Date;
  duration?: number; // dalam menit
  createdAt: Date;
}

export interface Tournament {
  _id?: string;
  name: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  participants: string[]; // array of player IDs
  status: 'upcoming' | 'ongoing' | 'completed';
  winnerId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Collection names
export const COLLECTIONS = {
  PLAYERS: 'players',
  GAMES: 'games',
  TOURNAMENTS: 'tournaments'
} as const;
