// Database Models dan Types untuk Domino League

export interface LeaguePlayer {
  id: number;
  name: string;
  MP: number;  // Matches Played
  W: number;   // Wins
  L: number;   // Losses
  SF: number;  // Score For
  SA: number;  // Score Against
  SD: number;  // Score Difference
  Pts: number; // Points
}

export interface League {
  _id?: string;
  name: string;
  data: LeaguePlayer[];
  slug: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface Game {
  _id?: string;
  leagueSlug: string;
  player1Id: number;
  player2Id: number;
  player1Score: number;
  player2Score: number;
  winnerId: number;
  gameDate: Date;
  duration?: number; // dalam menit
  createdAt: Date;
}

export interface Player {
  _id?: string;
  name: string;
  email?: string;
  playerId: number;
  createdAt: Date;
  updatedAt: Date;
}

// Collection names
export const COLLECTIONS = {
  LEAGUES: 'leagues',
  GAMES: 'league_games',
  PLAYERS: 'players'
} as const;
