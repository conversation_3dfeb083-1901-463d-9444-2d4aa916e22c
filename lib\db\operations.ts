import { getDatabase } from '../mongodb';
import { Player, Game, League, LeaguePlayer, COLLECTIONS } from './models';
import { ObjectId } from 'mongodb';

// League Operations
export class LeagueOperations {
  static async create(leagueData: Omit<League, '_id' | 'createdAt' | 'updatedAt'>): Promise<League> {
    const db = await getDatabase();
    const now = new Date();

    const league: Omit<League, '_id'> = {
      ...leagueData,
      createdAt: now,
      updatedAt: now
    };

    const result = await db.collection(COLLECTIONS.LEAGUES).insertOne(league);
    return { ...league, _id: result.insertedId.toString() };
  }

  static async findBySlug(slug: string): Promise<League | null> {
    const db = await getDatabase();
    const league = await db.collection(COLLECTIONS.LEAGUES).findOne({ slug }) as any;
    return league ? { ...league, _id: league._id.toString() } as League : null;
  }

  static async findById(id: string): Promise<League | null> {
    const db = await getDatabase();
    const league = await db.collection(COLLECTIONS.LEAGUES).findOne({ _id: new ObjectId(id) }) as any;
    return league ? { ...league, _id: league._id.toString() } as League : null;
  }

  static async findAll(): Promise<League[]> {
    const db = await getDatabase();
    const leagues = await db.collection(COLLECTIONS.LEAGUES).find({}).toArray() as any[];
    return leagues.map(league => ({ ...league, _id: league._id.toString() } as League));
  }

  static async updatePlayerStats(slug: string, playerId: number, stats: Partial<LeaguePlayer>): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.LEAGUES).updateOne(
      { slug, "data.id": playerId },
      {
        $set: {
          "data.$": { ...stats, id: playerId },
          updatedAt: new Date()
        }
      }
    );
    return result.modifiedCount === 1;
  }

  static async addPlayer(slug: string, player: LeaguePlayer): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.LEAGUES).updateOne(
      { slug },
      {
        $push: { data: player } as any,
        $set: { updatedAt: new Date() }
      }
    );
    return result.modifiedCount === 1;
  }

  static async removePlayer(slug: string, playerId: number): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.LEAGUES).updateOne(
      { slug },
      {
        $pull: { data: { id: playerId } } as any,
        $set: { updatedAt: new Date() }
      }
    );
    return result.modifiedCount === 1;
  }

  static async update(slug: string, updateData: Partial<Omit<League, '_id' | 'createdAt' | 'slug'>>): Promise<League | null> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.LEAGUES).findOneAndUpdate(
      { slug },
      { $set: { ...updateData, updatedAt: new Date() } },
      { returnDocument: 'after' }
    ) as any;
    return result ? { ...result, _id: result._id.toString() } as League : null;
  }
}

// Game Operations
export class GameOperations {
  static async create(gameData: Omit<Game, '_id' | 'createdAt'>): Promise<Game> {
    const db = await getDatabase();
    const game: Omit<Game, '_id'> = {
      ...gameData,
      createdAt: new Date()
    };

    const result = await db.collection(COLLECTIONS.GAMES).insertOne(game);

    // Update league player statistics
    await this.updateLeagueStats(gameData.leagueSlug, gameData.player1Id, gameData.player2Id, gameData.winnerId, gameData.player1Score, gameData.player2Score);

    return { ...game, _id: result.insertedId.toString() } as Game;
  }

  static async findById(id: string): Promise<Game | null> {
    const db = await getDatabase();
    const game = await db.collection(COLLECTIONS.GAMES).findOne({ _id: new ObjectId(id) }) as any;
    return game ? { ...game, _id: game._id.toString() } as Game : null;
  }

  static async findByLeague(leagueSlug: string): Promise<Game[]> {
    const db = await getDatabase();
    const games = await db.collection(COLLECTIONS.GAMES).find({ leagueSlug }).sort({ gameDate: -1 }).toArray() as any[];
    return games.map(game => ({ ...game, _id: game._id.toString() } as Game));
  }

  static async findByPlayer(leagueSlug: string, playerId: number): Promise<Game[]> {
    const db = await getDatabase();
    const games = await db.collection(COLLECTIONS.GAMES).find({
      leagueSlug,
      $or: [{ player1Id: playerId }, { player2Id: playerId }]
    }).sort({ gameDate: -1 }).toArray() as any[];
    return games.map(game => ({ ...game, _id: game._id.toString() } as Game));
  }

  static async findAll(): Promise<Game[]> {
    const db = await getDatabase();
    const games = await db.collection(COLLECTIONS.GAMES).find({}).sort({ gameDate: -1 }).toArray() as any[];
    return games.map(game => ({ ...game, _id: game._id.toString() } as Game));
  }

  private static async updateLeagueStats(leagueSlug: string, player1Id: number, player2Id: number, winnerId: number, player1Score: number, player2Score: number): Promise<void> {
    const league = await LeagueOperations.findBySlug(leagueSlug);
    if (!league) return;

    // Find players in league data
    const player1 = league.data.find(p => p.id === player1Id);
    const player2 = league.data.find(p => p.id === player2Id);

    if (!player1 || !player2) return;

    // Update player 1 stats
    const updatedPlayer1: LeaguePlayer = {
      ...player1,
      MP: player1.MP + 1,
      W: winnerId === player1Id ? player1.W + 1 : player1.W,
      L: winnerId === player1Id ? player1.L : player1.L + 1,
      SF: player1.SF + player1Score,
      SA: player1.SA + player2Score,
      SD: (player1.SF + player1Score) - (player1.SA + player2Score),
      Pts: winnerId === player1Id ? player1.Pts + 3 : player1.Pts
    };

    // Update player 2 stats
    const updatedPlayer2: LeaguePlayer = {
      ...player2,
      MP: player2.MP + 1,
      W: winnerId === player2Id ? player2.W + 1 : player2.W,
      L: winnerId === player2Id ? player2.L : player2.L + 1,
      SF: player2.SF + player2Score,
      SA: player2.SA + player1Score,
      SD: (player2.SF + player2Score) - (player2.SA + player1Score),
      Pts: winnerId === player2Id ? player2.Pts + 3 : player2.Pts
    };

    // Update both players in the league
    await LeagueOperations.updatePlayerStats(leagueSlug, player1Id, updatedPlayer1);
    await LeagueOperations.updatePlayerStats(leagueSlug, player2Id, updatedPlayer2);
  }
}

// Player Operations (for individual player records)
export class PlayerOperations {
  static async create(playerData: Omit<Player, '_id' | 'createdAt' | 'updatedAt'>): Promise<Player> {
    const db = await getDatabase();
    const now = new Date();

    const player: Omit<Player, '_id'> = {
      ...playerData,
      createdAt: now,
      updatedAt: now
    };

    const result = await db.collection(COLLECTIONS.PLAYERS).insertOne(player);
    return { ...player, _id: result.insertedId.toString() } as Player;
  }

  static async findById(id: string): Promise<Player | null> {
    const db = await getDatabase();
    const player = await db.collection(COLLECTIONS.PLAYERS).findOne({ _id: new ObjectId(id) }) as any;
    return player ? { ...player, _id: player._id.toString() } as Player : null;
  }

  static async findByPlayerId(playerId: number): Promise<Player | null> {
    const db = await getDatabase();
    const player = await db.collection(COLLECTIONS.PLAYERS).findOne({ playerId }) as any;
    return player ? { ...player, _id: player._id.toString() } as Player : null;
  }

  static async findAll(): Promise<Player[]> {
    const db = await getDatabase();
    const players = await db.collection(COLLECTIONS.PLAYERS).find({}).toArray() as any[];
    return players.map(player => ({ ...player, _id: player._id.toString() } as Player));
  }

  static async update(id: string, updateData: Partial<Omit<Player, '_id' | 'createdAt'>>): Promise<Player | null> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.PLAYERS).findOneAndUpdate(
      { _id: new ObjectId(id) },
      { $set: { ...updateData, updatedAt: new Date() } },
      { returnDocument: 'after' }
    ) as any;
    return result ? { ...result, _id: result._id.toString() } as Player : null;
  }

  static async delete(id: string): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.PLAYERS).deleteOne({ _id: new ObjectId(id) });
    return result.deletedCount === 1;
  }
}
