import { getDatabase } from '../mongodb';
import { Player, Game, Tournament, COLLECTIONS } from './models';
import { ObjectId } from 'mongodb';

// Player Operations
export class PlayerOperations {
  static async create(playerData: Omit<Player, '_id' | 'createdAt' | 'updatedAt'>): Promise<Player> {
    const db = await getDatabase();
    const now = new Date();
    
    const player: Omit<Player, '_id'> = {
      ...playerData,
      createdAt: now,
      updatedAt: now
    };

    const result = await db.collection(COLLECTIONS.PLAYERS).insertOne(player);
    return { ...player, _id: result.insertedId.toString() };
  }

  static async findById(id: string): Promise<Player | null> {
    const db = await getDatabase();
    const player = await db.collection(COLLECTIONS.PLAYERS).findOne({ _id: new ObjectId(id) });
    return player ? { ...player, _id: player._id.toString() } : null;
  }

  static async findByEmail(email: string): Promise<Player | null> {
    const db = await getDatabase();
    const player = await db.collection(COLLECTIONS.PLAYERS).findOne({ email });
    return player ? { ...player, _id: player._id.toString() } : null;
  }

  static async findAll(): Promise<Player[]> {
    const db = await getDatabase();
    const players = await db.collection(COLLECTIONS.PLAYERS).find({}).toArray();
    return players.map(player => ({ ...player, _id: player._id.toString() }));
  }

  static async update(id: string, updateData: Partial<Omit<Player, '_id' | 'createdAt'>>): Promise<Player | null> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.PLAYERS).findOneAndUpdate(
      { _id: new ObjectId(id) },
      { $set: { ...updateData, updatedAt: new Date() } },
      { returnDocument: 'after' }
    );
    return result ? { ...result, _id: result._id.toString() } : null;
  }

  static async delete(id: string): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.PLAYERS).deleteOne({ _id: new ObjectId(id) });
    return result.deletedCount === 1;
  }
}

// Game Operations
export class GameOperations {
  static async create(gameData: Omit<Game, '_id' | 'createdAt'>): Promise<Game> {
    const db = await getDatabase();
    const game: Omit<Game, '_id'> = {
      ...gameData,
      createdAt: new Date()
    };

    const result = await db.collection(COLLECTIONS.GAMES).insertOne(game);
    
    // Update player statistics
    await this.updatePlayerStats(gameData.player1Id, gameData.player2Id, gameData.winnerId);
    
    return { ...game, _id: result.insertedId.toString() };
  }

  static async findById(id: string): Promise<Game | null> {
    const db = await getDatabase();
    const game = await db.collection(COLLECTIONS.GAMES).findOne({ _id: new ObjectId(id) });
    return game ? { ...game, _id: game._id.toString() } : null;
  }

  static async findByPlayer(playerId: string): Promise<Game[]> {
    const db = await getDatabase();
    const games = await db.collection(COLLECTIONS.GAMES).find({
      $or: [{ player1Id: playerId }, { player2Id: playerId }]
    }).toArray();
    return games.map(game => ({ ...game, _id: game._id.toString() }));
  }

  static async findAll(): Promise<Game[]> {
    const db = await getDatabase();
    const games = await db.collection(COLLECTIONS.GAMES).find({}).sort({ gameDate: -1 }).toArray();
    return games.map(game => ({ ...game, _id: game._id.toString() }));
  }

  private static async updatePlayerStats(player1Id: string, player2Id: string, winnerId: string): Promise<void> {
    const db = await getDatabase();
    
    // Update both players' games played
    await db.collection(COLLECTIONS.PLAYERS).updateMany(
      { _id: { $in: [new ObjectId(player1Id), new ObjectId(player2Id)] } },
      { $inc: { gamesPlayed: 1 } }
    );

    // Update winner's wins
    await db.collection(COLLECTIONS.PLAYERS).updateOne(
      { _id: new ObjectId(winnerId) },
      { $inc: { wins: 1 } }
    );

    // Update loser's losses
    const loserId = winnerId === player1Id ? player2Id : player1Id;
    await db.collection(COLLECTIONS.PLAYERS).updateOne(
      { _id: new ObjectId(loserId) },
      { $inc: { losses: 1 } }
    );
  }
}

// Tournament Operations
export class TournamentOperations {
  static async create(tournamentData: Omit<Tournament, '_id' | 'createdAt' | 'updatedAt'>): Promise<Tournament> {
    const db = await getDatabase();
    const now = new Date();
    
    const tournament: Omit<Tournament, '_id'> = {
      ...tournamentData,
      createdAt: now,
      updatedAt: now
    };

    const result = await db.collection(COLLECTIONS.TOURNAMENTS).insertOne(tournament);
    return { ...tournament, _id: result.insertedId.toString() };
  }

  static async findById(id: string): Promise<Tournament | null> {
    const db = await getDatabase();
    const tournament = await db.collection(COLLECTIONS.TOURNAMENTS).findOne({ _id: new ObjectId(id) });
    return tournament ? { ...tournament, _id: tournament._id.toString() } : null;
  }

  static async findAll(): Promise<Tournament[]> {
    const db = await getDatabase();
    const tournaments = await db.collection(COLLECTIONS.TOURNAMENTS).find({}).sort({ startDate: -1 }).toArray();
    return tournaments.map(tournament => ({ ...tournament, _id: tournament._id.toString() }));
  }

  static async update(id: string, updateData: Partial<Omit<Tournament, '_id' | 'createdAt'>>): Promise<Tournament | null> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.TOURNAMENTS).findOneAndUpdate(
      { _id: new ObjectId(id) },
      { $set: { ...updateData, updatedAt: new Date() } },
      { returnDocument: 'after' }
    );
    return result ? { ...result, _id: result._id.toString() } : null;
  }

  static async addParticipant(tournamentId: string, playerId: string): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.TOURNAMENTS).updateOne(
      { _id: new ObjectId(tournamentId) },
      { $addToSet: { participants: playerId } }
    );
    return result.modifiedCount === 1;
  }

  static async removeParticipant(tournamentId: string, playerId: string): Promise<boolean> {
    const db = await getDatabase();
    const result = await db.collection(COLLECTIONS.TOURNAMENTS).updateOne(
      { _id: new ObjectId(tournamentId) },
      { $pull: { participants: playerId } }
    );
    return result.modifiedCount === 1;
  }
}
