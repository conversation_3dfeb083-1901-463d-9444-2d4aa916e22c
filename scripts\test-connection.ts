import { MongoClient } from 'mongodb';

async function testConnection() {
  const uri = process.env.MONGODB_URI;
  const dbName = process.env.MONGODB_DB_NAME;

  console.log('🔍 Testing MongoDB connection...');
  console.log('URI:', uri?.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')); // Hide credentials
  console.log('Database:', dbName);

  if (!uri) {
    console.error('❌ MONGODB_URI not found in environment variables');
    process.exit(1);
  }

  const client = new MongoClient(uri);

  try {
    console.log('🔌 Connecting to MongoDB...');
    await client.connect();
    
    console.log('✅ Connected successfully!');
    
    // Test ping
    const adminDb = client.db().admin();
    const pingResult = await adminDb.ping();
    console.log('🏓 Ping result:', pingResult);
    
    // Test database access
    const db = client.db(dbName);
    const collections = await db.listCollections().toArray();
    console.log('📁 Available collections:', collections.map(c => c.name));
    
    // Test basic operation
    const testCollection = db.collection('test');
    const insertResult = await testCollection.insertOne({ 
      test: true, 
      timestamp: new Date() 
    });
    console.log('✍️ Test insert result:', insertResult.insertedId);
    
    // Clean up test document
    await testCollection.deleteOne({ _id: insertResult.insertedId });
    console.log('🧹 Test document cleaned up');
    
    console.log('🎉 All tests passed! MongoDB connection is working correctly.');
    
  } catch (error) {
    console.error('❌ Connection failed:', error);
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      
      // Provide specific troubleshooting tips
      if (error.message.includes('ECONNREFUSED')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('- Check if MongoDB service is running');
        console.log('- Verify the connection string');
        console.log('- Check firewall settings');
      } else if (error.message.includes('authentication')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('- Check username and password');
        console.log('- Verify database user permissions');
        console.log('- Check if IP address is whitelisted (for Atlas)');
      } else if (error.message.includes('timeout')) {
        console.log('\n💡 Troubleshooting tips:');
        console.log('- Check network connectivity');
        console.log('- Verify Atlas cluster is running');
        console.log('- Check if IP address is whitelisted');
      }
    }
    
    process.exit(1);
  } finally {
    await client.close();
    console.log('🔌 Connection closed');
  }
}

testConnection();
