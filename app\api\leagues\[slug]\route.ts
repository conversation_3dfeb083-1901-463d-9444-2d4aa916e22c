import { NextRequest, NextResponse } from 'next/server';
import { LeagueOperations } from '@/lib/db/operations';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const league = await LeagueOperations.findBySlug(slug);
    
    if (!league) {
      return NextResponse.json(
        {
          success: false,
          message: 'League not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: league
    });
  } catch (error) {
    console.error('Error fetching league:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch league',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;
    const body = await request.json();
    const { name, data } = body;

    const league = await LeagueOperations.findBySlug(slug);
    if (!league) {
      return NextResponse.json(
        {
          success: false,
          message: 'League not found'
        },
        { status: 404 }
      );
    }

    const updatedLeague = await LeagueOperations.update(slug, {
      name,
      data
    });

    return NextResponse.json({
      success: true,
      data: updatedLeague
    });
  } catch (error) {
    console.error('Error updating league:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to update league',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
