'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// Helper function for consistent date formatting
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid Date';
  }
};

interface League {
  _id: string;
  name: string;
  slug: string;
  data: any[];
  createdAt?: string;
  updatedAt?: string;
}

export default function LeaguesPage() {
  const [leagues, setLeagues] = useState<League[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLeagues();
  }, []);

  const fetchLeagues = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/leagues');
      const data = await response.json();
      
      if (data.success) {
        setLeagues(data.data);
      } else {
        setError(data.message || 'Failed to fetch leagues');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading leagues...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">❌ Error</div>
          <p className="text-gray-300">{error}</p>
          <button
            onClick={fetchLeagues}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">🏆 Domino Leagues</h1>
          <p className="text-gray-400">Browse and manage all domino leagues</p>
        </div>

        {leagues.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-6xl mb-4">🏆</div>
            <h3 className="text-xl font-medium text-white mb-2">No leagues found</h3>
            <p className="text-gray-400">Create your first league to get started</p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {leagues.map((league) => (
              <Link
                key={league._id}
                href={`/leagues/${league.slug}`}
                className="block bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-700 overflow-hidden hover:bg-gray-750"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white truncate">
                      {league.name}
                    </h3>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-600 text-blue-100">
                      {league.data.length} players
                    </span>
                  </div>

                  <div className="space-y-2 text-sm text-gray-400">
                    <div className="flex items-center">
                      <span className="font-medium">Slug:</span>
                      <span className="ml-2 font-mono bg-gray-700 px-2 py-1 rounded text-xs text-gray-300">
                        {league.slug}
                      </span>
                    </div>
                    
                    {league.createdAt && (
                      <div className="flex items-center">
                        <span className="font-medium">Created:</span>
                        <span className="ml-2">
                          {formatDate(league.createdAt)}
                        </span>
                      </div>
                    )}

                    {league.updatedAt && (
                      <div className="flex items-center">
                        <span className="font-medium">Updated:</span>
                        <span className="ml-2">
                          {formatDate(league.updatedAt)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <div className="flex items-center text-blue-400 text-sm font-medium">
                      View League Table
                      <svg className="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
