import { NextRequest, NextResponse } from 'next/server';
import { PlayerOperations } from '@/lib/db/operations';

export async function GET() {
  try {
    const players = await PlayerOperations.findAll();
    return NextResponse.json({
      success: true,
      data: players
    });
  } catch (error) {
    console.error('Error fetching players:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch players',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, playerId } = body;

    if (!name || !playerId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Name and playerId are required'
        },
        { status: 400 }
      );
    }

    // Check if player already exists
    const existingPlayer = await PlayerOperations.findByPlayerId(playerId);
    if (existingPlayer) {
      return NextResponse.json(
        {
          success: false,
          message: 'Player with this ID already exists'
        },
        { status: 409 }
      );
    }

    const player = await PlayerOperations.create({
      name,
      email,
      playerId
    });

    return NextResponse.json({
      success: true,
      data: player
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating player:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create player',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
