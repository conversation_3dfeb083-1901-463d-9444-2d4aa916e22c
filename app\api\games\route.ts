import { NextRequest, NextResponse } from 'next/server';
import { GameOperations } from '@/lib/db/operations';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const leagueSlug = searchParams.get('league');
    const playerId = searchParams.get('player');

    let games;
    if (leagueSlug && playerId) {
      games = await GameOperations.findByPlayer(leagueSlug, parseInt(playerId));
    } else if (leagueSlug) {
      games = await GameOperations.findByLeague(leagueSlug);
    } else {
      games = await GameOperations.findAll();
    }

    return NextResponse.json({
      success: true,
      data: games
    });
  } catch (error) {
    console.error('Error fetching games:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch games',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      leagueSlug, 
      player1Id, 
      player2Id, 
      player1Score, 
      player2Score, 
      winnerId,
      gameDate = new Date(),
      duration 
    } = body;

    if (!leagueSlug || !player1Id || !player2Id || player1Score === undefined || player2Score === undefined || !winnerId) {
      return NextResponse.json(
        {
          success: false,
          message: 'leagueSlug, player1Id, player2Id, scores, and winnerId are required'
        },
        { status: 400 }
      );
    }

    const game = await GameOperations.create({
      leagueSlug,
      player1Id: parseInt(player1Id),
      player2Id: parseInt(player2Id),
      player1Score: parseInt(player1Score),
      player2Score: parseInt(player2Score),
      winnerId: parseInt(winnerId),
      gameDate: new Date(gameDate),
      duration: duration ? parseInt(duration) : undefined
    });

    return NextResponse.json({
      success: true,
      data: game
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating game:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to create game',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
