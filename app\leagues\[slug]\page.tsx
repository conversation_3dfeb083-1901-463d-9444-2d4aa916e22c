'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';

interface LeaguePlayer {
  id: number;
  name: string;
  MP: number;  // Matches Played
  W: number;   // Wins
  L: number;   // Losses
  SF: number;  // Score For
  SA: number;  // Score Against
  SD: number;  // Score Difference
  Pts: number; // Points
}

interface League {
  _id: string;
  name: string;
  data: LeaguePlayer[];
  slug: string;
  createdAt?: string;
  updatedAt?: string;
}

export default function LeaguePage() {
  const params = useParams();
  const slug = params.slug as string;
  
  const [league, setLeague] = useState<League | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLeague = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/leagues/${slug}`);
      const data = await response.json();

      if (data.success) {
        setLeague(data.data);
      } else {
        setError(data.message || 'Failed to fetch league');
      }
    } catch {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    if (slug) {
      fetchLeague();
    }
  }, [slug, fetchLeague]);

  // Sort players by points (descending), then by score difference
  const sortedPlayers = league?.data.sort((a, b) => {
    if (b.Pts !== a.Pts) return b.Pts - a.Pts;
    return b.SD - a.SD;
  }) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading league...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">❌ Error</div>
          <p className="text-gray-300">{error}</p>
          <button
            onClick={fetchLeague}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!league) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-500 text-6xl mb-4">🏆</div>
          <h3 className="text-xl font-medium text-white mb-2">League not found</h3>
          <p className="text-gray-400">The league you&apos;re looking for doesn&apos;t exist</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-xl font-bold text-white mb-2">League Table</h1>
          <p className="text-gray-400">League standings and statistics</p>
        </div>

        {sortedPlayers.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 text-6xl mb-4">👥</div>
            <h3 className="text-xl font-medium text-white mb-2">No players in this league</h3>
            <p className="text-gray-400">Add players to start tracking the league</p>
          </div>
        ) : (
          <div className="bg-gray-800 shadow-sm rounded-lg overflow-hidden border border-gray-700">
            <div className="px-6 py-4 border-b border-gray-700">
              <h2 className="font-bold text-center text-white text-2xl">{league.name}</h2>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-600">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Player
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      MP
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      W
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      L
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      SF
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      SA
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      SD
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Pts
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-800 divide-y divide-gray-600">
                  {sortedPlayers.map((player, index) => (
                    <tr key={player.id} className={`hover:bg-gray-700 transition ${index < 3 ? 'bg-blue-900/20' : ''}`}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-white">
                            {player.name}
                          </div>
                          <div className="text-xs text-gray-400 ml-2">
                            ({player.id})
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 text-center">
                        {player.MP}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-400 text-center font-medium">
                        {player.W}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-400 text-center font-medium">
                        {player.L}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 text-center">
                        {player.SF}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 text-center">
                        {player.SA}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm text-center font-medium ${
                        player.SD >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {player.SD >= 0 ? '+' : ''}{player.SD}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-600 text-blue-100">
                          {player.Pts}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
